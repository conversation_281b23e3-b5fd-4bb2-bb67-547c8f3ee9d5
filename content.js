// 네이버 항공권 API 요청을 감지하고 데이터를 시각화하는 컨텐츠 스크립트

class NaverFlightVisualizer {
  constructor() {
    this.apiData = [];
    this.isActive = false;
    this.selectedAirlines = new Set();
    this.selectedSchedule = null;
    this.excludeEdgeCards = true; // 엣지카드 제외
    this.dragSetup = false; // 드래그 기능 설정 여부
    this.leftPanelCollapsed = false; // 좌측 패널 접힘 상태
    this.init();
  }

  init() {
    this.injectScript();
    this.setupMessageListener();
    this.createVisualizationPanel();
    this.setupToggleButton();
  }

  // API 요청을 감지하기 위한 스크립트 주입
  injectScript() {
    // console.log('[CONTENT] 스크립트 주입 시작');
    
    // DOM이 준비될 때까지 기다린 후 주입
    const injectWhenReady = () => {
      const script = document.createElement('script');
      script.src = chrome.runtime.getURL('injected.js');
      script.onload = () => {
        // console.log('[CONTENT] injected.js 로드 완료');
        script.remove();
      };
      script.onerror = (error) => {
        console.error('[CONTENT] injected.js 로드 실패:', error);
      };
      
      const target = document.head || document.documentElement;
      if (target) {
        target.appendChild(script);
        // console.log('[CONTENT] 스크립트 태그 추가됨');
      } else {
        console.error('[CONTENT] 주입할 타겟을 찾을 수 없음');
        // 1초 후 재시도
        setTimeout(injectWhenReady, 1000);
      }
    };
    
    if (document.readyState === 'loading') {
      document.addEventListener('DOMContentLoaded', injectWhenReady);
    } else {
      injectWhenReady();
    }
  }

  // 주입된 스크립트로부터 메시지 수신
  setupMessageListener() {
    // console.log('[CONTENT] 메시지 리스너 설정 중...');
    
    window.addEventListener('message', (event) => {
      // 소스 검증
      if (event.source !== window) return;
      
      // console.log('[CONTENT] 메시지 수신:', event.data);
      
      if (event.data.type === 'NAVER_FLIGHT_API_DATA') {
        // console.log('[CONTENT] GraphQL API 데이터 메시지 처리');
        this.handleApiData(event.data.payload);
      }
    });
    
    // console.log('[CONTENT] 메시지 리스너 설정 완료');
  }

  // API 데이터 처리
  handleApiData(data) {
    // console.log('네이버 항공권 GraphQL API 데이터:', data);
    
    // GraphQL getInternationalList 응답 처리
    if (data.response?.data?.internationalList?.results?.fares) {
      this.processInternationalFares(data.response.data.internationalList.results);
    }
  }

  // 여행사 코드 매핑
  getAgentName(agtCode) {
    const agentMap = {
      'INT005': 'NOL인터파크투어',
      'ONT003': '온라인투어',
      'HAT004': '하나투어',
      'MRT023': '마이리얼트립',
      'WEB001': '웹투어',
      'WHY002': '와이페이모어',
      'MOT007': '모두투어',
      'YEL009': '노랑풍선',
      'CTR013': '트립닷컴',
      'LTT017': '롯데관광',
      'KRT006': '여행이지',
      'TOV025': '투어비스',
      'NIT015': '내일투어',
      'HDT028': '더현대트래블',
    };
    return agentMap[agtCode] || agtCode;
  }

  // 스케줄 키에서 항공사 코드 추출
  extractAirlineCodesFromScheduleKey(scheduleKey) {
    // 스케줄 키 형태: "20250618ICNKIXTW0303|20250624KIXICNTW0302:"
    // 끝에 콜론이 있을 수 있으므로 제거
    const cleanedKey = scheduleKey.replace(/[:]+$/, '');
    const segments = cleanedKey.split('|');
    const airlineCodes = new Set();

    segments.forEach(segment => {
      if (segment.length >= 20) {
        // 항공사 코드는 14~16번째 자리 (2글자)
        const airline = segment.substring(14, 16);
        if (airline) {
          airlineCodes.add(airline);
        }
      }
    });

    return Array.from(airlineCodes);
  }

  // GraphQL internationalList 응답 처리
  processInternationalFares(results) {
    const { fares, schedules, airlines } = results;
    const processedData = [];
    
    // airlines 정보를 저장
    this.airlinesData = airlines || {};

    // fares 객체를 순회하며 각 항공편의 요금 정보 처리
    Object.entries(fares).forEach(([scheduleKey, fareData]) => {
      const { fare } = fareData;

      // 각 운임 타입별로 처리
      Object.entries(fare).forEach(([fareType, fareList]) => {
        fareList.forEach(fareItem => {
          const totalPrice = parseInt(fareItem.Adult?.Fare || 0) + 
                           parseInt(fareItem.Adult?.Tax || 0) + 
                           parseInt(fareItem.Adult?.QCharge || 0);
          
          if (totalPrice > 0) {
            // 카드 할인 정보 분석
            const isGeneralAdult = fareType === 'A01'; // 일반 성인 요금
            const isCardDiscount = fareType.includes('/B'); // 카드 할인 요금
            const cardCode = isCardDiscount ? fareType.split('/')[1] : null;
            
            processedData.push({
              scheduleKey,
              fareType,
              agent: this.getAgentName(fareItem.AgtCode),
              agentCode: fareItem.AgtCode,
              price: totalPrice,
              fare: parseInt(fareItem.Adult?.Fare || 0),
              tax: parseInt(fareItem.Adult?.Tax || 0),
              qcharge: parseInt(fareItem.Adult?.QCharge || 0),
              confirmType: fareItem.ConfirmType,
              baggageType: fareItem.BaggageType,
              isGeneralAdult,
              isCardDiscount,
              cardCode,
              timestamp: Date.now()
            });
          }
        });
      });
    });

    // 기존 데이터에 추가 (중복 제거 - 더 정확한 키 조합 사용)
    const newData = processedData.filter(newItem =>
      !this.apiData.some(existingItem =>
        existingItem.scheduleKey === newItem.scheduleKey &&
        existingItem.agentCode === newItem.agentCode &&
        existingItem.fareType === newItem.fareType &&
        existingItem.price === newItem.price
      )
    );

    // 새로운 데이터만 추가
    if (newData.length > 0) {
      this.apiData = [...this.apiData, ...newData];
      console.log(`[CONTENT] 새로운 요금 데이터 ${newData.length}개 추가됨. 총 ${this.apiData.length}개`);

      // 데이터가 추가된 경우에만 시각화 업데이트
      this.updateVisualization();
    } else {
      console.log(`[CONTENT] 중복 데이터로 인해 추가된 데이터 없음. 총 ${this.apiData.length}개 유지`);
    }
  }

  // 시각화 패널 생성
  createVisualizationPanel() {
    const panel = document.createElement('div');
    panel.id = 'naver-flight-viz-panel';
    panel.innerHTML = `
      <div class="viz-header">
        <h3>항공권 데이터 분석</h3>
        <button id="viz-close">×</button>
      </div>
      <div class="viz-content">
        <div class="viz-left-panel">
          <div class="left-panel-header">
            <h3>필터 & 스케줄</h3>
            <button id="left-panel-toggle" class="panel-toggle-btn">◀</button>
          </div>
          <div class="left-panel-content">
            <div id="airline-filter">
              <h4>항공사 필터</h4>
              <div id="airline-tags"></div>
            </div>
            <div id="schedule-list">
              <h4>스케줄 목록</h4>
              <div id="schedule-items"></div>
            </div>
          </div>
        </div>
        <div class="viz-right-panel">
          <div id="card-agent-chart">
            <div class="card-chart-header">
              <h4>선택된 스케줄의 카드별 할인율</h4>
              <div class="card-filter-toggle">
                <label class="toggle-switch">
                  <input type="checkbox" id="edge-card-toggle">
                  <span class="toggle-slider"></span>
                  <span class="toggle-label">엣지카드 제외</span>
                </label>
              </div>
            </div>
            <div id="selected-schedule-info"></div>
            <div id="card-agent-bars"></div>
          </div>
        </div>
      </div>
    `;

    document.body.appendChild(panel);

    // 닫기 버튼 이벤트
    document.getElementById('viz-close').addEventListener('click', () => {
      this.togglePanel(false);
    });

    // 카드 필터 토글 이벤트
    document.getElementById('edge-card-toggle').addEventListener('change', (e) => {
      this.excludeEdgeCards = e.target.checked;
      this.updateCardAgentChart();
    });

    // 좌측 패널 토글 이벤트
    document.getElementById('left-panel-toggle').addEventListener('click', () => {
      this.toggleLeftPanel();
    });

    // 드래그 기능은 패널이 열릴 때 설정
  }

  // 좌측 패널 토글
  toggleLeftPanel() {
    const leftPanel = document.querySelector('.viz-left-panel');
    const leftPanelContent = document.querySelector('.left-panel-content');
    const toggleBtn = document.getElementById('left-panel-toggle');
    const rightPanel = document.querySelector('.viz-right-panel');

    this.leftPanelCollapsed = !this.leftPanelCollapsed;

    if (this.leftPanelCollapsed) {
      leftPanel.classList.add('collapsed');
      leftPanelContent.style.display = 'none';
      toggleBtn.textContent = '▶';
      toggleBtn.title = '패널 펼치기';
      rightPanel.style.width = 'calc(100% - 60px)';
    } else {
      leftPanel.classList.remove('collapsed');
      leftPanelContent.style.display = 'block';
      toggleBtn.textContent = '◀';
      toggleBtn.title = '패널 접기';
      rightPanel.style.width = 'calc(100% - 350px)';
    }
  }

  // 드래그 기능 설정
  setupDragFunctionality(panel) {
    // 이미 설정되었으면 중복 설정 방지
    if (this.dragSetup) {
      return;
    }

    const header = document.getElementById('viz-header');
    if (!header) {
      console.error('[CONTENT] 헤더 요소를 찾을 수 없습니다.');
      return;
    }

    this.dragSetup = true;
    console.log('[CONTENT] 드래그 기능 설정됨');

    let isDragging = false;
    let currentX = 0;
    let currentY = 0;
    let initialX = 0;
    let initialY = 0;
    let xOffset = 0;
    let yOffset = 0;

    // 헤더에 드래그 커서 스타일 추가
    header.style.cursor = 'move';

    // 마우스 다운 이벤트
    header.addEventListener('mousedown', (e) => {
      // 닫기 버튼 클릭 시 드래그 방지
      if (e.target.id === 'viz-close') {
        console.log('[CONTENT] 닫기 버튼 클릭, 드래그 방지');
        return;
      }

      e.preventDefault();
      initialX = e.clientX - xOffset;
      initialY = e.clientY - yOffset;

      if (e.target === header || header.contains(e.target)) {
        isDragging = true;
        header.style.cursor = 'grabbing';
        console.log('[CONTENT] 드래그 시작, initialX:', initialX, 'initialY:', initialY);
      }
    });

    // 마우스 업 이벤트
    document.addEventListener('mouseup', () => {
      if (isDragging) {
        initialX = currentX || 0;
        initialY = currentY || 0;
        isDragging = false;
        header.style.cursor = 'move';
        console.log('[CONTENT] 마우스 업, 드래그 종료');
      }
    });

    // 마우스 이동 이벤트
    document.addEventListener('mousemove', (e) => {
      if (isDragging) {
        e.preventDefault();
        currentX = e.clientX - initialX;
        currentY = e.clientY - initialY;
        xOffset = currentX;
        yOffset = currentY;

        // 화면 경계 체크
        const rect = panel.getBoundingClientRect();
        const maxX = window.innerWidth - rect.width;
        const maxY = window.innerHeight - rect.height;

        // 경계 제한
        currentX = Math.max(0, Math.min(currentX, maxX));
        currentY = Math.max(0, Math.min(currentY, maxY));

        panel.style.transform = `translate(${currentX}px, ${currentY}px)`;
        console.log('[CONTENT] 드래그 중, currentX:', currentX, 'currentY:', currentY);
      }
    });

    // 터치 이벤트 (모바일 지원)
    header.addEventListener('touchstart', (e) => {
      if (e.target.id === 'viz-close') {
        return;
      }

      const touch = e.touches[0];
      initialX = touch.clientX - xOffset;
      initialY = touch.clientY - yOffset;

      if (e.target === header || header.contains(e.target)) {
        isDragging = true;
      }
    });

    document.addEventListener('touchend', () => {
      initialX = currentX;
      initialY = currentY;
      isDragging = false;
    });

    document.addEventListener('touchmove', (e) => {
      if (isDragging) {
        e.preventDefault();
        const touch = e.touches[0];
        currentX = touch.clientX - initialX;
        currentY = touch.clientY - initialY;
        xOffset = currentX;
        yOffset = currentY;

        // 화면 경계 체크
        const rect = panel.getBoundingClientRect();
        const maxX = window.innerWidth - rect.width;
        const maxY = window.innerHeight - rect.height;

        // 경계 제한
        currentX = Math.max(0, Math.min(currentX, maxX));
        currentY = Math.max(0, Math.min(currentY, maxY));

        panel.style.transform = `translate(${currentX}px, ${currentY}px)`;
      }
    });
  }

  // 토글 버튼 생성
  setupToggleButton() {
    // 기존 버튼이 있으면 제거
    const existingButton = document.getElementById('naver-flight-viz-toggle');
    if (existingButton) {
      existingButton.remove();
    }

    const button = document.createElement('button');
    button.id = 'naver-flight-viz-toggle';
    button.innerHTML = '📊';
    button.title = '항공권 데이터 시각화 토글';

    // 인라인 스타일로 강제 적용
    button.style.cssText = `
      position: fixed !important;
      bottom: 20px !important;
      right: 20px !important;
      z-index: 999999 !important;
      width: 60px !important;
      height: 60px !important;
      border-radius: 50% !important;
      background: #4285f4 !important;
      color: white !important;
      border: none !important;
      font-size: 24px !important;
      cursor: pointer !important;
      box-shadow: 0 4px 12px rgba(0,0,0,0.3) !important;
      transition: all 0.3s ease !important;
      display: block !important;
      visibility: visible !important;
      opacity: 1 !important;
    `;

    button.addEventListener('click', () => {
      this.togglePanel();
    });

    button.addEventListener('mouseenter', () => {
      button.style.background = '#3367d6';
      button.style.transform = 'scale(1.1)';
    });

    button.addEventListener('mouseleave', () => {
      button.style.background = '#4285f4';
      button.style.transform = 'scale(1)';
    });

    document.body.appendChild(button);
    console.log('[CONTENT] 토글 버튼 생성됨:', button);
  }

  // 패널 토글
  togglePanel(force = null) {
    const panel = document.getElementById('naver-flight-viz-panel');
    this.isActive = force !== null ? force : !this.isActive;
    panel.style.display = this.isActive ? 'block' : 'none';

    if (this.isActive) {
      this.updateVisualization();
      // 패널이 열릴 때 드래그 기능 설정
      setTimeout(() => {
        this.setupDragFunctionality(panel);
      }, 50);
    }
  }

  // 시각화 업데이트
  updateVisualization() {
    if (!this.isActive) return;

    this.updateAirlineFilter();
    this.updateScheduleList();
    this.updateCardAgentChart();
  }

  // 항공사 필터 업데이트
  updateAirlineFilter() {
    const container = document.getElementById('airline-tags');

    // fares 키에서 항공사 코드 추출
    const availableAirlines = new Set();

    // 먼저 airlines 데이터에서 모든 항공사 추출
    if (this.airlinesData && typeof this.airlinesData === 'object') {
      Object.keys(this.airlinesData).forEach(code => {
        availableAirlines.add(code);
      });
    }

    // console.log('Available airlines before API data:', Array.from(availableAirlines));

    // API 데이터에서 스케줄 키를 파싱해서 항공사 코드 추출
    this.apiData.forEach(fare => {
      if (fare.scheduleKey) {
        const airlineCodes = this.extractAirlineCodesFromScheduleKey(fare.scheduleKey);
        airlineCodes.forEach(code => availableAirlines.add(code));
      }
    });

    // console.log('Available airlines:', Array.from(availableAirlines));
    // console.log('Airlines data:', this.airlinesData);

    // 항공사 태그 생성
    container.innerHTML = '';
    if (availableAirlines.size === 0) {
      container.innerHTML = '<p class="no-airlines">항공사 데이터가 없습니다.</p>';
      return;
    }

    // 전체 선택/해제 버튼
    const allButton = document.createElement('button');
    allButton.className = 'airline-tag all-tag';
    allButton.textContent = this.selectedAirlines.size === 0 ? '전체 선택' : '전체 해제';
    allButton.addEventListener('click', () => {
      if (this.selectedAirlines.size === 0) {
        // 전체 선택
        availableAirlines.forEach(code => this.selectedAirlines.add(code));
      } else {
        // 전체 해제
        this.selectedAirlines.clear();
      }
      this.updateVisualization();
    });
    container.appendChild(allButton);

    // 각 항공사별 태그 (airlines 정보 활용)
    Array.from(availableAirlines).sort().forEach(airlineCode => {
      const tag = document.createElement('button');
      tag.className = `airline-tag ${this.selectedAirlines.has(airlineCode) ? 'selected' : ''}`;

      tag.textContent = airlineCode;
      tag.title = airlineCode;

      tag.addEventListener('click', () => {
        if (this.selectedAirlines.has(airlineCode)) {
          this.selectedAirlines.delete(airlineCode);
        } else {
          this.selectedAirlines.add(airlineCode);
        }
        this.updateVisualization();
      });
      container.appendChild(tag);
    });
  }

  // 스케줄 목록 업데이트
  updateScheduleList() {
    const container = document.getElementById('schedule-items');

    // 항공사 필터 적용된 데이터
    const filteredData = this.selectedAirlines.size === 0 ?
      this.apiData :
      this.apiData.filter(fare => {
        if (fare.scheduleKey) {
          const airlineCodes = this.extractAirlineCodesFromScheduleKey(fare.scheduleKey);
          return airlineCodes.some(code => this.selectedAirlines.has(code));
        }
        return false;
      });

    // 스케줄별로 그룹화
    const scheduleGroups = {};
    filteredData.forEach(fare => {
      if (!scheduleGroups[fare.scheduleKey]) {
        scheduleGroups[fare.scheduleKey] = {
          scheduleKey: fare.scheduleKey,
          fares: [],
          airlines: this.extractAirlineCodesFromScheduleKey(fare.scheduleKey),
          minPrice: Infinity,
          maxPrice: 0,
          agentCount: new Set(),
          cardDiscountCount: 0,
          hasINT005: false  // INT005 Fare 존재 여부 추가
        };
      }

      const group = scheduleGroups[fare.scheduleKey];
      group.fares.push(fare);
      group.agentCount.add(fare.agentCode);

      // INT005 Fare 존재 여부 확인
      if (fare.agentCode === 'INT005') {
        group.hasINT005 = true;
      }

      if (fare.price < group.minPrice) group.minPrice = fare.price;
      if (fare.price > group.maxPrice) group.maxPrice = fare.price;
      if (fare.isCardDiscount) group.cardDiscountCount++;
    });

    // INT005 Fare가 없는 스케줄들 제외
    const filteredScheduleGroups = {};
    Object.entries(scheduleGroups).forEach(([scheduleKey, group]) => {
      if (group.hasINT005) {
        filteredScheduleGroups[scheduleKey] = group;
      }
    });

    // 컨테이너 초기화
    container.innerHTML = '';

    if (Object.keys(filteredScheduleGroups).length === 0) {
      container.innerHTML = '<div class="no-data">인터파크항공 운임이 존재하지 않습니다.</div>';
      return;
    }

    // 스케줄 아이템 생성
    Object.values(filteredScheduleGroups).forEach(schedule => {
      const scheduleItem = document.createElement('div');
      scheduleItem.className = `schedule-item ${this.selectedSchedule === schedule.scheduleKey ? 'selected' : ''}`;

      // 항공사명 가져오기
      const airlineNames = schedule.airlines.map(code => {
        if (this.airlinesData && this.airlinesData[code]) {
          const airlineInfo = this.airlinesData[code];
          if (typeof airlineInfo === 'object' && airlineInfo.name) {
            return airlineInfo.name;
          } else if (typeof airlineInfo === 'string') {
            return airlineInfo;
          }
        }
        return code;
      });

      // 할인율 계산 (해당 스케줄의 모든 카드 할인 요금들을 INT005 A01 기준으로 계산)
      const scheduleDiscountRates = [];
      // INT005 A01 기준 가격 찾기
      const int005GeneralFare = schedule.fares.find(f =>
        f.agentCode === 'INT005' && f.isGeneralAdult
      );

      if (int005GeneralFare) {
        // 모든 여행사의 모든 카드 할인 요금을 INT005 A01 기준으로 할인율 계산
        schedule.fares.forEach(fare => {
          if (fare.isCardDiscount) {
            const discountRate = ((int005GeneralFare.price - fare.price) / int005GeneralFare.price) * 100;
            if (discountRate > 0) {
              scheduleDiscountRates.push(discountRate);
            }
          }
        });
      }

      // 모든 fares에서 가장 큰 할인율을 maxDiscount로 설정
      const maxDiscount = scheduleDiscountRates.length > 0 ? Math.max(...scheduleDiscountRates) : 0;

      // 스케줄 키 파싱
      const scheduleInfo = this.parseScheduleKey(schedule.scheduleKey);

      // segment 리스트 HTML 생성
      const segmentsHtml = scheduleInfo.segments.map((segment, index) => {
        return `
          <div class="segment-item">
            <div class="segment-header">
              <span class="segment-number">${segment.segmentIndex}</span>
              <span class="flight-number">${segment.flightNumber}</span>
              <span class="segment-date">${segment.date}</span>
            </div>
            <div class="segment-route">
              <span class="departure-airport">${segment.departure}</span>
              <span class="route-arrow">✈</span>
              <span class="arrival-airport">${segment.arrival}</span>
            </div>
          </div>
        `;
      }).join('');

      scheduleItem.innerHTML = `
        <div class="schedule-card">
          <div class="schedule-card-header">
            <div class="flight-info">
              <div class="total-route">${scheduleInfo.totalRoute}</div>
              <div class="airline-name">${airlineNames.join(' + ')}</div>
              <div class="price-badge">${schedule.minPrice.toLocaleString()}원 ~ ${schedule.maxPrice.toLocaleString()}원</div>
            </div>
            <div class="schedule-discount-badge ${maxDiscount >= 10 ? 'high-discount' : maxDiscount >= 5 ? 'medium-discount' : 'low-discount'}">
              최대 ${maxDiscount.toFixed(1)}%
            </div>
          </div>

          <div class="schedule-card-body">
            <div class="segments-list">
              ${segmentsHtml}
            </div>
          </div>
        </div>
      `;

      // 클릭 이벤트
      scheduleItem.addEventListener('click', () => {
        // 기존 선택 해제
        container.querySelectorAll('.schedule-item').forEach(item => {
          item.classList.remove('selected');
        });

        // 새로운 선택
        scheduleItem.classList.add('selected');
        this.selectedSchedule = schedule.scheduleKey;

        // 우측 패널 업데이트
        this.updateCardAgentChart();
      });

      container.appendChild(scheduleItem);
    });
  }

  // 선택된 스케줄 해제
  clearSelectedSchedule() {
    this.selectedSchedule = null;

    // 좌측 패널의 선택 상태 해제
    const container = document.getElementById('schedule-items');
    if (container) {
      container.querySelectorAll('.schedule-item').forEach(item => {
        item.classList.remove('selected');
      });
    }

    // 우측 패널 업데이트
    this.updateCardAgentChart();
  }

  // 할인율 계산 (항공사 필터 및 선택된 스케줄 적용)
  calculateDiscountRates() {
    const discountRates = [];

    // 항공사 필터 적용
    let filteredData = this.selectedAirlines.size === 0 ?
      this.apiData :
      this.apiData.filter(fare => {
        // 스케줄 키에서 항공사 코드를 추출해서 선택된 항공사와 비교
        if (fare.scheduleKey) {
          const airlineCodes = this.extractAirlineCodesFromScheduleKey(fare.scheduleKey);
          return airlineCodes.some(code => this.selectedAirlines.has(code));
        }
        return false;
      });

    // 선택된 스케줄 필터 적용
    if (this.selectedSchedule) {
      filteredData = filteredData.filter(fare => fare.scheduleKey === this.selectedSchedule);
    }

    // 스케줄별로 INT005의 A01 기준 요금 찾기
    const int005BaseRates = {};
    filteredData.forEach(fare => {
      if (fare.agentCode === 'INT005' && fare.isGeneralAdult) {
        int005BaseRates[fare.scheduleKey] = fare.price;
      }
    });

    // 스케줄별, 여행사별로 그룹화 (카드 할인 요금과 일반 성인 요금)
    const groupedData = {};

    filteredData.forEach(fare => {
      const key = `${fare.scheduleKey}_${fare.agentCode}`;
      if (!groupedData[key]) {
        groupedData[key] = { cardFares: [], generalFare: null };
      }

      if (fare.isCardDiscount) {
        groupedData[key].cardFares.push(fare);
      } else if (fare.isGeneralAdult) {
        groupedData[key].generalFare = fare;
      }
    });

    // 할인율 계산 (INT005 A01 기준)
    Object.entries(groupedData).forEach(([key, group]) => {
      const scheduleKey = key.split('_')[0];
      const basePrice = int005BaseRates[scheduleKey];

      if (basePrice && group.cardFares.length > 0) {
        group.cardFares.forEach(cardFare => {
          const discountRate = ((basePrice - cardFare.price) / basePrice) * 100;
          if (discountRate > 0) {
            discountRates.push({
              discountRate,
              cardCode: cardFare.cardCode,
              agent: cardFare.agent,
              agentCode: cardFare.agentCode,
              generalPrice: basePrice, // INT005 A01 기준 가격
              cardPrice: cardFare.price,
              scheduleKey: cardFare.scheduleKey,
              agentGeneralPrice: group.generalFare ? group.generalFare.price : null // 해당 여행사의 성인 요금
            });
          }
        });
      }
    });

    return discountRates;
  }

  // 카드별 여행사 할인율 매트릭스 차트 업데이트
  updateCardAgentChart() {
    const container = document.getElementById('card-agent-bars');
    const infoContainer = document.getElementById('selected-schedule-info');

    container.innerHTML = '';

    // 선택된 스케줄이 없으면 카드 데이터를 표시하지 않음
    if (!this.selectedSchedule) {
      infoContainer.innerHTML = `
        <div style="background: #fff3cd; padding: 10px; border-radius: 6px; margin-bottom: 15px; border-left: 4px solid #ffc107;">
          <div style="font-size: 12px; color: #856404;">좌측에서 스케줄을 선택하세요</div>
        </div>
      `;
      return;
    }

    // 선택된 스케줄 정보 표시
    const airlineCodes = this.extractAirlineCodesFromScheduleKey(this.selectedSchedule);
    const airlineNames = airlineCodes.map(code => {
      if (this.airlinesData && this.airlinesData[code]) {
        const airlineInfo = this.airlinesData[code];
        if (typeof airlineInfo === 'object' && airlineInfo.name) {
          return airlineInfo.name;
        } else if (typeof airlineInfo === 'string') {
          return airlineInfo;
        }
      }
      return code;
    });

    // 선택된 스케줄 정보 파싱
    const selectedScheduleInfo = this.parseScheduleKey(this.selectedSchedule);

    infoContainer.innerHTML = `
      <div style="background: #f8f9fa; padding: 10px; border-radius: 6px; margin-bottom: 15px; border-left: 4px solid #4285f4; position: relative;">
        <button id="clear-schedule-btn" style="
          position: absolute;
          top: 8px;
          right: 8px;
          background: #dc3545;
          color: white;
          border: none;
          border-radius: 50%;
          width: 20px;
          height: 20px;
          font-size: 12px;
          cursor: pointer;
          display: flex;
          align-items: center;
          justify-content: center;
          line-height: 1;
        " title="스케줄 선택 해제">×</button>
        <div style="font-size: 13px; font-weight: 600; color: #333; margin-right: 25px;">${airlineNames.join(', ')}</div>
        <div style="font-size: 11px; color: #888; margin-top: 2px;">${selectedScheduleInfo.totalRoute}</div>
      </div>
    `;

    // X 버튼 클릭 이벤트 추가
    const clearBtn = document.getElementById('clear-schedule-btn');
    if (clearBtn) {
      clearBtn.addEventListener('click', (e) => {
        e.stopPropagation(); // 이벤트 버블링 방지
        this.clearSelectedSchedule();
      });
    }

    // 카드 매핑 정보 사용
    const cardMap = {
      'B01': { name: 'KB국민카드', edgeCard: true },
      'B02': { name: '롯데카드', edgeCard: true },
      'B03': { name: '신한카드 The CLASSIC+', edgeCard: false },
      'B04': { name: 'BC카드', edgeCard: true },
      'B05': { name: '삼성카드', edgeCard: true },
      'B06': { name: '외환카드', edgeCard: true },
      'B07': { name: '하나 트래블GO VISA 체크카드', edgeCard: false },
      'B08': { name: '하나 트래블로그 SKYPASS', edgeCard: false },
      'B09': { name: '현대카드', edgeCard: true },
      'B10': { name: '하나 트래블로그 PRESTIGE', edgeCard: false },
      'B11': { name: '씨티카드', edgeCard: true },
      'B12': { name: '우리카드', edgeCard: true },
      'B13': { name: 'NH채움카드', edgeCard: true },
      'B14': { name: '롯데DIGILOCA SKYPASS(AMEX)', edgeCard: false },
      'B19': { name: '신한카드', edgeCard: true },
      'B22': { name: 'KB국민 AMEX카드', edgeCard: false },
      'B25': { name: 'IBK기업은행카드', edgeCard: true },
      'B26': { name: '하나/외환카드', edgeCard: false },
      'B27': { name: '글로벌쇼핑 삼성카드', edgeCard: false },
      'B31': { name: '하나카드', edgeCard: true },
      'B32': { name: 'KB국민 청춘대로 티타늄카드', edgeCard: false },
      'B37': { name: 'KB국민/씨티/NH채움카드', edgeCard: false },
      'B40': { name: 'KB국민 비자카드', edgeCard: false },
      'B47': { name: '하나멤버스 1Q 카드 Daily', edgeCard: false },
      'B50': { name: 'KB국민 청춘대로 1코노미카드', edgeCard: false },
      'B51': { name: '신한카드 Air 1.5', edgeCard: false },
      'B55': { name: '하나멤버스 1Q Tour1 카드', edgeCard: false },
      'B56': { name: '글로벌쇼핑 삼성카드 5 V2', edgeCard: false },
      'B57': { name: '삼성카드 taptap I', edgeCard: false },
      'B59': { name: 'SKYPASS THE DREAM 롯데카드', edgeCard: false },
      'B62': { name: '하나 BC카드', edgeCard: false },
      'B65': { name: '아메리칸 엑스프레스 그린', edgeCard: true },
      'B70': { name: '유니온페이 카드', edgeCard: true },
      'B71': { name: 'KB국민 톡톡PAY카드', edgeCard: false },
      'B72': { name: 'NH농협카드', edgeCard: false },
      'B74': { name: '네이버페이', edgeCard: true },
      'B77': { name: 'BC바로카드', edgeCard: false },
      'B79': { name: '현대 the Red Edition2(실적충족시)', edgeCard: false },
      'B80': { name: '현대 M2/M3 Edition2(실적충족시)', edgeCard: false },
      'B81': { name: '부산은행 BC카드', edgeCard: false },
      'B82': { name: '대구은행 BC카드', edgeCard: false },
      'B83': { name: '현대카드 the Pink', edgeCard: false },
      'B84': { name: 'KB국민 WAVVE', edgeCard: false },
      'B85': { name: 'KB국민 스카이패스 티타늄', edgeCard: false },
      'B86': { name: 'KB국민 체크카드', edgeCard: false },
      'B87': { name: '하나 트래블로그 신용카드', edgeCard: false },
      'B88': { name: '삼성 iD PET 카드', edgeCard: false },
      'B89': { name: '현대 AMEX 카드', edgeCard: false },
      'B90': { name: 'KB국민 몰테일카드', edgeCard: false },
      'B91': { name: 'KB국민카드(실적충족시)', edgeCard: false },
      'B92': { name: '삼성 iD NOMAD 카드', edgeCard: false },
      'B93': { name: '롯데 Trip to 로카 카드', edgeCard: false },
      'B94': { name: '우리 카드의 정석 EVERY1', edgeCard: false },
      'B95': { name: 'Mile1 하나카드', edgeCard: false },
      'B96': { name: '삼성 iD GLOBAL 카드', edgeCard: false },
      'B97': { name: 'KB국민 트래블러스 체크카드(Master)', edgeCard: false },
      'B98': { name: 'JADE Classic 하나카드', edgeCard: false },
      'B99': { name: '신한 SOL트래블 체크카드', edgeCard: false }
    };

    const discountRates = this.calculateDiscountRates();

    // 카드별 여행사별 할인율 매트릭스 생성
    const cardAgentMatrix = {};
    discountRates.forEach(rate => {
      if (!cardAgentMatrix[rate.cardCode]) {
        cardAgentMatrix[rate.cardCode] = {};
      }
      if (!cardAgentMatrix[rate.cardCode][rate.agent]) {
        cardAgentMatrix[rate.cardCode][rate.agent] = {
          discountRates: [],
          agentGeneralPrice: rate.agentGeneralPrice
        };
      }
      cardAgentMatrix[rate.cardCode][rate.agent].discountRates.push(rate.discountRate);
      // 여행사 성인 요금이 있으면 업데이트 (가장 최근 값 사용)
      if (rate.agentGeneralPrice) {
        cardAgentMatrix[rate.cardCode][rate.agent].agentGeneralPrice = rate.agentGeneralPrice;
      }
    });

    // 전체 최대 할인율 계산 (모든 카드에서의 절대 최대값)
    let globalMaxDiscount = 0;
    Object.values(cardAgentMatrix).forEach(agents => {
      if (agents && typeof agents === 'object') {
        Object.values(agents).forEach(agentData => {
          if (agentData.discountRates && Array.isArray(agentData.discountRates) && agentData.discountRates.length > 0) {
            const avgDiscount = agentData.discountRates.reduce((sum, rate) => sum + rate, 0) / agentData.discountRates.length;
            if (avgDiscount > globalMaxDiscount) {
              globalMaxDiscount = avgDiscount;
            }
          }
        });
      }
    });

    // 최소 기준값 설정 (10% 이상으로 설정하여 의미있는 비교 가능)
    const minBaseDiscount = 10;
    const maxBaseDiscount = Math.max(globalMaxDiscount, minBaseDiscount);

    // 모든 카드에 대해 섹션 생성 (edgeCard 필터 적용)
    Object.entries(cardMap).forEach(([cardCode, cardInfo]) => {
      // edgeCard 필터 적용
      if (this.excludeEdgeCards && !cardInfo.edgeCard) {
        return;
      }

      const cardSection = document.createElement('div');
      cardSection.className = 'card-section';

      const cardHeader = document.createElement('div');
      cardHeader.className = 'card-header';
      cardHeader.style.cursor = 'pointer';

      const agents = cardAgentMatrix[cardCode];
      const hasData = agents && Object.keys(agents).length > 0;

      // INT005 Fare가 없는 카드는 표시하지 않음
      const hasINT005 = agents && agents['NOL인터파크투어'];
      if (!hasData || !hasINT005) {
        return;
      }

      let maxDiscount = 0;
      let agentCount = 0;
      const agentDiscounts = Object.values(agents)
        .filter(agentData => agentData.discountRates && Array.isArray(agentData.discountRates) && agentData.discountRates.length > 0)
        .map(agentData => agentData.discountRates.reduce((sum, rate) => sum + rate, 0) / agentData.discountRates.length);
      maxDiscount = agentDiscounts.length > 0 ? Math.max(...agentDiscounts) : 0;
      agentCount = Object.keys(agents).length;

      // 최대 할인율 표시
      let discountText = '데이터 없음';
      if (hasData) {
        discountText = `최대 ${maxDiscount.toFixed(1)}% (${agentCount}개사)`;
      }

      cardHeader.innerHTML = `
        <div style="display: flex; align-items: center; gap: 8px;">
          <span class="toggle-icon">${hasData ? '▼' : '▶'}</span>
          <span class="card-name">${cardInfo.name}</span>
        </div>
        <span class="max-discount">${discountText}</span>
      `;

      const agentBars = document.createElement('div');
      agentBars.className = 'agent-bars';
      agentBars.style.display = 'none'; // 기본적으로 접힘

      if (hasData) {
        const agentDiscounts = Object.entries(agents).map(([agent, agentData]) => ({
          agent,
          avgDiscount: agentData.discountRates.reduce((sum, rate) => sum + rate, 0) / agentData.discountRates.length,
          count: agentData.discountRates.length,
          agentGeneralPrice: agentData.agentGeneralPrice
        })).sort((a, b) => b.avgDiscount - a.avgDiscount);

        agentDiscounts.forEach((agentData, index) => {
          const agentBar = document.createElement('div');
          const rank = index + 1;
          const isINT005 = agentData.agent === 'NOL인터파크투어';
          const isFirst = rank === 1;

          let barClasses = 'agent-bar';
          if (isFirst) barClasses += ' rank-first';
          if (isINT005) barClasses += ' int005-agent';

          agentBar.className = barClasses;
          const rankSuffix = '위';

          let rankClasses = 'agent-rank';
          if (isFirst) rankClasses += ' rank-first-badge';
          if (isINT005) rankClasses += ' int005-badge';

          // 해당 카드의 실제 카드 할인 운임 찾기
          const cardDiscountFares = discountRates.filter(rate =>
            rate.cardCode === cardCode && rate.agent === agentData.agent
          );

          let cardPriceDisplay = '카드운임없음';
          if (cardDiscountFares.length > 0) {
            // 여러 카드 운임이 있는 경우 최저가 표시
            const minCardPrice = Math.min(...cardDiscountFares.map(fare => fare.cardPrice));
            cardPriceDisplay = `${minCardPrice.toLocaleString()}원`;
          }

          // 성인 금액 표시 (있는 경우)
          const generalPriceDisplay = agentData.agentGeneralPrice ?
            `모든 결제수단: ${agentData.agentGeneralPrice.toLocaleString()}원` :
            '일반가격없음';

          // 할인율에 따른 바 색상 결정
          let barColor = '#e0e0e0'; // 기본 회색
          if (agentData.avgDiscount >= 15) {
            barColor = '#4caf50'; // 15% 이상: 녹색
          } else if (agentData.avgDiscount >= 10) {
            barColor = '#ff9800'; // 10-15%: 주황색
          } else if (agentData.avgDiscount >= 5) {
            barColor = '#2196f3'; // 5-10%: 파란색
          }

          const barWidth = maxBaseDiscount > 0 ? Math.min((agentData.avgDiscount / maxBaseDiscount) * 100, 100) : 0;

          agentBar.innerHTML = `
            <div class="agent-info">
              <span class="agent-rank ${rankClasses}">${rank}${rankSuffix}</span>
              <span class="agent-name">${agentData.agent}</span>
              <span class="agent-discount">${agentData.avgDiscount.toFixed(1)}%</span>
            </div>
            <div class="agent-bar-fill" style="width: ${barWidth}%; background-color: ${barColor};"></div>
            <div class="agent-prices">
              <span class="agent-card-price">${cardPriceDisplay}</span>
              <span class="agent-general-price">${generalPriceDisplay}</span>
            </div>
          `;
          agentBars.appendChild(agentBar);
        });
      } else {
        agentBars.innerHTML = '<div class="no-data">할인 데이터가 없습니다.</div>';
      }

      // 클릭 이벤트로 접기/펼치기
      cardHeader.addEventListener('click', () => {
        const isVisible = agentBars.style.display !== 'none';
        agentBars.style.display = isVisible ? 'none' : 'block';
        const toggleIcon = cardHeader.querySelector('.toggle-icon');
        toggleIcon.textContent = isVisible ? '▶' : '▼';
      });
      
      cardSection.appendChild(cardHeader);
      cardSection.appendChild(agentBars);
      container.appendChild(cardSection);
    });
  }

  // 스케줄 키 파싱하여 의미있는 정보 추출 (파이프로 구분된 segment들 처리)
  parseScheduleKey(scheduleKey) {
    // 스케줄 키 형태: "20250618ICNKIXTW0303|20250624KIXICNTW0302"
    const segments = scheduleKey.split('|');

    const result = {
      segments: [],
      totalRoute: '',
      totalTimeRange: '',
      formatted: scheduleKey,
      isMultiSegment: segments.length > 1
    };

    segments.forEach((segment, index) => {
      const segmentInfo = this.parseSegment(segment);
      if (segmentInfo) {
        result.segments.push({
          ...segmentInfo,
          segmentIndex: index + 1
        });
      }
    });

    // 전체 경로 및 날짜 정보 생성
    if (result.segments.length > 0) {
      const firstSegment = result.segments[0];
      const lastSegment = result.segments[result.segments.length - 1];

      result.totalRoute = result.segments.map(seg => seg.departure).join(' → ') + ' → ' + lastSegment.arrival;

      // 날짜 범위 표시 (출발일 ~ 도착일)
      if (firstSegment.date === lastSegment.date) {
        result.totalTimeRange = firstSegment.date; // 같은 날이면 하나만 표시
      } else {
        result.totalTimeRange = `${firstSegment.date} ~ ${lastSegment.date}`;
      }

      result.date = firstSegment.date; // 첫 번째 segment의 날짜 사용
    }

    return result;
  }

  // 개별 segment 파싱
  parseSegment(segment) {
    // segment 형태 예시: "20250618ICNKIXTW0303"
    // 형태: 날짜(8자리) + 출발지(3자리) + 도착지(3자리) + 항공사(2자리) + 편명(4자리)
    const segmentInfo = {
      flightNumber: '',
      date: '',
      departure: '',
      arrival: '',
      departureTime: '',
      arrivalTime: '',
      airline: '',
      route: '',
      timeRange: '',
      formatted: segment
    };

    if (segment.length >= 20) {
      // 날짜 (8자리): YYYYMMDD
      segmentInfo.date = segment.substring(0, 8);

      // 출발지 (3자리): ICN
      segmentInfo.departure = segment.substring(8, 11);

      // 도착지 (3자리): KIX
      segmentInfo.arrival = segment.substring(11, 14);

      // 항공사 코드 (2자리): TW
      segmentInfo.airline = segment.substring(14, 16);

      // 편명 (4자리): 0303
      const flightNum = segment.substring(16, 20);
      segmentInfo.flightNumber = segmentInfo.airline + flightNum;

      // 날짜 포맷팅 (YYYYMMDD -> YYYY.MM.DD)
      if (segmentInfo.date.length === 8) {
        segmentInfo.date = `${segmentInfo.date.substring(0, 4)}.${segmentInfo.date.substring(4, 6)}.${segmentInfo.date.substring(6, 8)}`;
      }

      // 시간 정보는 scheduleKey에 없으므로 기본값 설정
      segmentInfo.departureTime = '--:--';
      segmentInfo.arrivalTime = '--:--';
      segmentInfo.timeRange = `${segmentInfo.departureTime} → ${segmentInfo.arrivalTime}`;

      // 공항 코드 그대로 사용
      segmentInfo.route = `${segmentInfo.departure} → ${segmentInfo.arrival}`;

      return segmentInfo;
    }

    return null;
  }
}

// 페이지 로드 완료 후 초기화
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', () => {
    new NaverFlightVisualizer();
  });
} else {
  new NaverFlightVisualizer();
}