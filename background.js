// 백그라운드 서비스 워커

chrome.runtime.onInstalled.addListener(() => {
  console.log('네이버 항공권 데이터 시각화 익스텐션이 설치되었습니다.');
});

// 메시지 리스너
chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
  if (request.action === 'saveFlightData') {
    // 항공편 데이터 저장
    chrome.storage.local.get(['flightData'], (result) => {
      const existingData = result.flightData || [];
      const newData = [...existingData, ...request.data];
      
      // 중복 제거 (같은 항공편의 경우)
      const uniqueData = newData.filter((flight, index, self) => 
        index === self.findIndex(f => 
          f.airline === flight.airline && 
          f.departure === flight.departure && 
          f.arrival === flight.arrival
        )
      );
      
      chrome.storage.local.set({
        flightData: uniqueData,
        lastUpdate: Date.now()
      }, () => {
        sendResponse({success: true, count: uniqueData.length});
      });
    });
    return true; // 비동기 응답을 위해 true 반환
  }
  
  if (request.action === 'getFlightData') {
    chrome.storage.local.get(['flightData'], (result) => {
      sendResponse({data: result.flightData || []});
    });
    return true;
  }
});

// 탭 업데이트 감지
chrome.tabs.onUpdated.addListener((tabId, changeInfo, tab) => {
  if (changeInfo.status === 'complete' && 
      tab.url && tab.url.includes('flight.naver.com')) {
    // 네이버 항공권 페이지가 로드되면 컨텐츠 스크립트 활성화
    chrome.tabs.sendMessage(tabId, {action: 'pageLoaded'});
  }
});

// 브라우저 액션 클릭 (필요시)
chrome.action.onClicked.addListener((tab) => {
  if (tab.url && tab.url.includes('flight.naver.com')) {
    chrome.tabs.sendMessage(tab.id, {action: 'toggleVisualization'});
  }
});