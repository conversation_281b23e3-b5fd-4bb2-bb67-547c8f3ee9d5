// 팝업 스크립트

document.addEventListener('DOMContentLoaded', function() {
  const toggleBtn = document.getElementById('toggle-btn');
  const clearDataBtn = document.getElementById('clear-data');
  const statusElement = document.getElementById('current-status');
  const dataCountElement = document.getElementById('data-count');
  
  // 현재 탭 정보 가져오기
  chrome.tabs.query({active: true, currentWindow: true}, function(tabs) {
    const currentTab = tabs[0];
    
    // 네이버 항공권 사이트인지 확인
    if (currentTab.url && currentTab.url.includes('flight.naver.com')) {
      statusElement.textContent = '활성 (네이버 항공권)';
      statusElement.style.color = '#34a853';
      toggleBtn.disabled = false;
    } else {
      statusElement.textContent = '비활성 (네이버 항공권 사이트가 아님)';
      statusElement.style.color = '#ea4335';
      toggleBtn.disabled = true;
      toggleBtn.textContent = '네이버 항공권 사이트에서 사용 가능';
    }
  });
  
  // 저장된 데이터 개수 표시
  chrome.storage.local.get(['flightData'], function(result) {
    const dataCount = result.flightData ? result.flightData.length : 0;
    dataCountElement.textContent = `${dataCount}개 항공편`;
  });
  
  // 시각화 패널 토글
  toggleBtn.addEventListener('click', function() {
    chrome.tabs.query({active: true, currentWindow: true}, function(tabs) {
      chrome.tabs.sendMessage(tabs[0].id, {
        action: 'toggleVisualization'
      }, function(response) {
        if (chrome.runtime.lastError) {
          console.error('메시지 전송 실패:', chrome.runtime.lastError);
        } else {
          toggleBtn.textContent = response && response.isActive ? 
            '시각화 패널 닫기' : '시각화 패널 열기';
        }
      });
    });
  });
  
  // 데이터 초기화
  clearDataBtn.addEventListener('click', function() {
    if (confirm('수집된 모든 데이터를 삭제하시겠습니까?')) {
      chrome.storage.local.clear(function() {
        dataCountElement.textContent = '0개 항공편';
        
        // 컨텐츠 스크립트에 데이터 초기화 메시지 전송
        chrome.tabs.query({active: true, currentWindow: true}, function(tabs) {
          chrome.tabs.sendMessage(tabs[0].id, {
            action: 'clearData'
          });
        });
      });
    }
  });
  
  // 실시간 데이터 업데이트 리스너
  chrome.storage.onChanged.addListener(function(changes, namespace) {
    if (namespace === 'local' && changes.flightData) {
      const newCount = changes.flightData.newValue ? 
        changes.flightData.newValue.length : 0;
      dataCountElement.textContent = `${newCount}개 항공편`;
    }
  });
});