<!DOCTYPE html>
<html lang="ko">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>네이버 항공권 데이터 시각화</title>
    <style>
        body {
            width: 300px;
            height: 400px;
            margin: 0;
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
            background: #f8f9fa;
        }
        
        .header {
            background: #4285f4;
            color: white;
            padding: 20px;
            text-align: center;
        }
        
        .header h1 {
            margin: 0;
            font-size: 16px;
            font-weight: 500;
        }
        
        .content {
            padding: 20px;
        }
        
        .feature-list {
            list-style: none;
            padding: 0;
            margin: 0;
        }
        
        .feature-list li {
            padding: 8px 0;
            border-bottom: 1px solid #e0e0e0;
            display: flex;
            align-items: center;
        }
        
        .feature-list li:last-child {
            border-bottom: none;
        }
        
        .feature-icon {
            margin-right: 10px;
            font-size: 16px;
        }
        
        .feature-text {
            font-size: 14px;
            color: #333;
        }
        
        .status {
            background: white;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 15px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }
        
        .status-title {
            font-size: 14px;
            font-weight: 600;
            color: #333;
            margin-bottom: 5px;
        }
        
        .status-value {
            font-size: 12px;
            color: #666;
        }
        
        .btn {
            width: 100%;
            padding: 12px;
            background: #4285f4;
            color: white;
            border: none;
            border-radius: 6px;
            font-size: 14px;
            cursor: pointer;
            margin-top: 10px;
        }
        
        .btn:hover {
            background: #3367d6;
        }
        
        .btn:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>항공권 데이터 시각화</h1>
    </div>
    
    <div class="content">
        <div class="status">
            <div class="status-title">현재 상태</div>
            <div class="status-value" id="current-status">비활성</div>
        </div>
        
        <ul class="feature-list">
            <li>
                <span class="feature-icon">📊</span>
                <span class="feature-text">실시간 카드 할인율 모니터링</span>
            </li>
        </ul>
    </div>
    
    <script src="popup.js"></script>
</body>
</html>