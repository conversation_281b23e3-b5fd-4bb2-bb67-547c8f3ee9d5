/* 네이버 항공권 데이터 시각화 스타일 */

#naver-flight-viz-toggle {
  position: fixed !important;
  bottom: 20px !important;
  right: 20px !important;
  z-index: 999999 !important;
  width: 60px !important;
  height: 60px !important;
  border-radius: 50% !important;
  border: none !important;
  background: #4285f4 !important;
  color: white !important;
  font-size: 24px !important;
  cursor: pointer !important;
  box-shadow: 0 4px 12px rgba(0,0,0,0.3) !important;
  transition: all 0.3s ease !important;
  display: block !important;
  visibility: visible !important;
  opacity: 1 !important;
}

#naver-flight-viz-toggle:hover {
  background: #3367d6 !important;
  transform: scale(1.1) !important;
}

#naver-flight-viz-panel {
  position: fixed;
  top: 80px;
  left: 50%;
  transform: translateX(-50%);
  width: 1000px;
  max-width: 90vw;
  max-height: 700px;
  background: white;
  border: 1px solid #ddd;
  border-radius: 8px;
  box-shadow: 0 8px 24px rgba(0,0,0,0.15);
  z-index: 9999;
  display: none;
  overflow: hidden;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
}

.viz-header {
  background: #4285f4;
  color: white;
  padding: 15px 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.viz-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 500;
}

#viz-close {
  background: none;
  border: none;
  color: white;
  font-size: 20px;
  cursor: pointer;
  padding: 0;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 4px;
}

#viz-close:hover {
  background: rgba(255,255,255,0.2);
}

.viz-content {
  display: flex;
  height: 620px;
  overflow: hidden;
}

.viz-left-panel {
  width: 350px;
  border-right: 1px solid #e0e0e0;
  overflow-y: auto;
  background: #f8f9fa;
  transition: width 0.3s ease;
  position: relative;
}

.viz-left-panel.collapsed {
  width: 60px;
}

.left-panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 20px;
  background: #fff;
  border-bottom: 1px solid #e0e0e0;
  margin-bottom: 20px;
}

.left-panel-header h3 {
  margin: 0;
  font-size: 14px;
  font-weight: 600;
  color: #333;
}

.panel-toggle-btn {
  background: #4285f4;
  color: white;
  border: none;
  width: 24px;
  height: 24px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background 0.2s ease;
}

.panel-toggle-btn:hover {
  background: #3367d6;
}

.left-panel-content {
  padding: 0 20px;
}

.viz-right-panel {
  width: calc(100% - 350px);
  padding: 20px;
  overflow-y: auto;
  background: white;
  transition: width 0.3s ease;
}

.viz-stats {
  margin-bottom: 20px;
}

.stat-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid #f0f0f0;
}

.stat-item:last-child {
  border-bottom: none;
}

.stat-label {
  font-size: 14px;
  color: #666;
}

.stat-value {
  font-size: 14px;
  font-weight: 600;
  color: #333;
}

.viz-charts {
  margin-top: 20px;
}

#price-chart {
  width: 100%;
  height: 200px;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  margin-bottom: 20px;
}

#card-agent-chart {
  margin-bottom: 25px;
}

#card-agent-chart h4 {
  margin: 0 0 15px 0;
  font-size: 14px;
  font-weight: 600;
  color: #333;
}

/* 카드별 여행사 할인율 매트릭스 차트 */
.card-section {
  margin-bottom: 20px;
  border: 1px solid #e1e5e9;
  border-radius: 8px;
  overflow: hidden;
  background: #fff;
}

.card-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 12px 16px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  cursor: pointer;
  transition: background 0.2s ease;
}

.card-header:hover {
  background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
}

.toggle-icon {
  font-size: 12px;
  color: white;
  transition: transform 0.2s ease;
}

.card-header .card-name {
  font-size: 13px;
  font-weight: 600;
  color: white;
}

.max-discount {
  font-size: 12px;
  font-weight: 500;
  background: rgba(255,255,255,0.2);
  padding: 4px 8px;
  border-radius: 12px;
}

.agent-bars {
  padding: 12px;
  background: #f8f9fa;
}

.agent-bar {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
  gap: 8px;
  padding: 6px 8px;
  background: white;
  border-radius: 6px;
  box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.agent-bar:last-child {
  margin-bottom: 0;
}

.agent-info {
  display: flex;
  flex-direction: column;
  min-width: 90px;
}

.agent-rank {
  font-size: 9px;
  color: #666;
  font-weight: 500;
  margin-bottom: 1px;
  background: #f8f9fa;
  padding: 1px 4px;
  border-radius: 3px;
  align-self: flex-start;
  line-height: 1;
}

.agent-rank.rank-first-badge {
  background: #ffd700;
  color: #b8860b;
  font-weight: 600;
  box-shadow: 0 1px 2px rgba(0,0,0,0.1);
}

.agent-bar.rank-first {
  background: linear-gradient(135deg, #fff9e6, #ffffff);
  border: 1px solid #ffd700;
  box-shadow: 0 2px 4px rgba(255, 215, 0, 0.2);
}

.agent-rank.int005-badge {
  background: #e3f2fd;
  color: #1976d2;
  font-weight: 600;
  box-shadow: 0 1px 2px rgba(25, 118, 210, 0.1);
}

.agent-bar.int005-agent {
  background: linear-gradient(135deg, #f3f9ff, #ffffff);
  border: 1px solid #bbdefb;
  box-shadow: 0 2px 4px rgba(33, 150, 243, 0.15);
}

.agent-name {
  font-size: 11px;
  font-weight: 600;
  color: #333;
  line-height: 1.2;
}

.agent-discount {
  font-size: 10px;
  color: #e74c3c;
  font-weight: 600;
  margin-top: 1px;
}

.agent-bar-fill {
  background: linear-gradient(90deg, #ff6b6b, #ee5a24);
  height: 12px;
  border-radius: 6px;
  min-width: 3px;
  transition: width 0.3s ease;
  flex-grow: 1;
  box-shadow: inset 0 1px 2px rgba(0,0,0,0.1);
}

.agent-prices {
  display: flex;
  flex-direction: column;
  gap: 2px;
  min-width: 120px;
  text-align: right;
}

.agent-card-price {
  font-size: 10px;
  color: #d32f2f;
  background: #ffebee;
  padding: 2px 6px;
  border-radius: 8px;
  font-weight: 600;
}

.agent-general-price {
  font-size: 9px;
  color: #666;
  background: #f5f5f5;
  padding: 1px 4px;
  border-radius: 6px;
  font-weight: 500;
}

.agent-price {
  font-size: 10px;
  color: #2e7d32;
  min-width: 80px;
  text-align: right;
  background: #e8f5e8;
  padding: 2px 6px;
  border-radius: 10px;
  font-weight: 600;
}

.agent-count {
  font-size: 10px;
  color: #666;
  min-width: 30px;
  text-align: right;
  background: #f1f3f4;
  padding: 2px 6px;
  border-radius: 10px;
}

.no-data {
  text-align: center;
  color: #999;
  font-size: 12px;
  padding: 16px;
  font-style: italic;
}

/* 스케줄 목록 스타일 */
#schedule-list {
  margin-top: 20px;
}

#schedule-list h4 {
  margin: 0 0 15px 0;
  font-size: 14px;
  font-weight: 600;
  color: #333;
}

.schedule-item {
  margin-bottom: 12px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.schedule-item:hover .schedule-card {
  border-color: #4285f4;
  box-shadow: 0 4px 12px rgba(66, 133, 244, 0.15);
  transform: translateY(-1px);
}

.schedule-item.selected .schedule-card {
  border-color: #4285f4;
  background: #f8f9ff;
  box-shadow: 0 4px 16px rgba(66, 133, 244, 0.25);
}

.schedule-card {
  background: white;
  border: 2px solid #e0e0e0;
  border-radius: 12px;
  overflow: hidden;
  transition: all 0.3s ease;
}

.schedule-card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  background: linear-gradient(135deg, #f8f9fa, #ffffff);
  border-bottom: 1px solid #e0e0e0;
}

.flight-info {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.route-summary {
  display: flex;
  align-items: center;
  gap: 8px;
  flex-wrap: wrap;
}

.total-route {
  font-size: 16px;
  font-weight: 700;
  color: #1976d2;
  font-family: 'Courier New', monospace;
}

.flight-type-badge {
  font-size: 10px;
  font-weight: 600;
  padding: 2px 6px;
  border-radius: 10px;
  color: white;
}

.flight-type-badge.direct {
  background: linear-gradient(135deg, #4caf50, #45a049);
}

.flight-type-badge.connection {
  background: linear-gradient(135deg, #ff9800, #f57c00);
}

.journey-time {
  font-size: 12px;
  color: #666;
  font-weight: 500;
  background: #f0f7ff;
  padding: 2px 6px;
  border-radius: 8px;
  border: 1px solid #e3f2fd;
  align-self: flex-start;
}

.flight-number {
  font-size: 18px;
  font-weight: 700;
  color: #1976d2;
  font-family: 'Courier New', monospace;
}

.airline-name {
  font-size: 12px;
  color: #666;
  font-weight: 500;
}

.price-badge {
  font-size: 10px;
  background: #e3f2fd;
  color: #1976d2;
  padding: 2px 6px;
  border-radius: 10px;
  font-weight: 600;
  align-self: flex-start;
}

.schedule-discount-badge {
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 11px;
  font-weight: 600;
  color: white;
}

.schedule-discount-badge.high-discount {
  background: linear-gradient(135deg, #4caf50, #45a049);
}

.schedule-discount-badge.medium-discount {
  background: linear-gradient(135deg, #ff9800, #f57c00);
}

.schedule-discount-badge.low-discount {
  background: linear-gradient(135deg, #2196f3, #1976d2);
}

.schedule-card-body {
  padding: 16px;
  display: flex;
  flex-direction: column;
  gap: 12px;
}

/* Segments 리스트 스타일 */
.segments-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
  background: #f8f9fa;
  padding: 12px;
  border-radius: 8px;
  border: 1px solid #e0e0e0;
}

.segment-item {
  background: white;
  padding: 10px;
  border-radius: 6px;
  border: 1px solid #e0e0e0;
  transition: all 0.2s ease;
}

.segment-item:hover {
  border-color: #4285f4;
  box-shadow: 0 2px 8px rgba(66, 133, 244, 0.1);
}

.segment-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 6px;
}

.segment-number {
  background: #4285f4;
  color: white;
  font-size: 10px;
  font-weight: 600;
  padding: 2px 6px;
  border-radius: 10px;
  min-width: 20px;
  text-align: center;
}

.segment-header .flight-number {
  font-size: 14px;
  font-weight: 600;
  color: #1976d2;
  font-family: 'Courier New', monospace;
}

.segment-date {
  font-size: 12px;
  color: #666;
  font-family: 'Courier New', monospace;
  margin-left: auto;
  background: #f0f7ff;
  padding: 2px 6px;
  border-radius: 4px;
  border: 1px solid #e3f2fd;
}

.segment-route {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.segment-route .departure-airport,
.segment-route .arrival-airport {
  font-size: 14px;
  font-weight: 600;
  color: #333;
  font-family: 'Courier New', monospace;
  background: #e3f2fd;
  padding: 4px 8px;
  border-radius: 6px;
  border: 1px solid #bbdefb;
}

.segment-route .route-arrow {
  font-size: 12px;
  color: #1976d2;
}

.segment-divider {
  text-align: center;
  color: #666;
  font-size: 14px;
  margin: 4px 0;
}

/* 스케줄 요약 정보 */
.schedule-summary {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 12px;
  flex-wrap: wrap;
}

.total-time {
  font-size: 16px;
  font-weight: 600;
  color: #1976d2;
  font-family: 'Courier New', monospace;
}

.route-info {
  text-align: center;
}

.route-main {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
  margin-bottom: 4px;
}

.departure-airport, .arrival-airport {
  font-size: 20px;
  font-weight: 700;
  color: #333;
  font-family: 'Courier New', monospace;
  background: #f0f7ff;
  padding: 8px 12px;
  border-radius: 8px;
  border: 2px solid #e3f2fd;
}

.route-arrow {
  font-size: 16px;
  color: #1976d2;
}

.route-korean {
  font-size: 14px;
  color: #666;
  font-weight: 500;
}

.time-info {
  text-align: center;
  background: #f8f9fa;
  padding: 12px;
  border-radius: 8px;
}

.time-main {
  font-size: 18px;
  font-weight: 600;
  color: #1976d2;
  font-family: 'Courier New', monospace;
  margin-bottom: 4px;
}

.flight-date {
  font-size: 12px;
  color: #666;
}

.price-summary {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  flex-wrap: wrap;
  background: #f0f7ff;
  padding: 10px;
  border-radius: 8px;
  border: 1px solid #e3f2fd;
}

.min-price {
  font-size: 16px;
  font-weight: 600;
  color: #2e7d32;
}

.price-separator {
  color: #666;
  font-size: 14px;
}

.max-price {
  font-size: 14px;
  font-weight: 500;
  color: #666;
}

.price-diff {
  font-size: 11px;
  color: #888;
}

.schedule-stats {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 4px;
  background: #f1f3f4;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 10px;
}

.stat-icon {
  font-size: 12px;
}

.stat-text {
  color: #666;
  font-weight: 500;
}

.schedule-card-footer {
  padding: 8px 16px;
  background: #f8f9fa;
  border-top: 1px solid #e0e0e0;
}

.schedule-id {
  font-size: 10px;
  color: #888;
  font-family: 'Courier New', monospace;
}

/* 항공사 필터 스타일 */
#airline-filter {
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 1px solid #e0e0e0;
}

#airline-filter h4 {
  margin: 0 0 10px 0;
  font-size: 14px;
  font-weight: 600;
  color: #333;
}

#airline-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
}

.airline-tag {
  padding: 4px 10px;
  border: 1px solid #d0d0d0;
  border-radius: 16px;
  background: #f8f9fa;
  color: #666;
  font-size: 11px;
  cursor: pointer;
  transition: all 0.2s ease;
  min-width: 40px;
  text-align: center;
}

.airline-tag:hover {
  background: #e9ecef;
  border-color: #b0b0b0;
}

.airline-tag.selected {
  background: #4285f4;
  color: white;
  border-color: #4285f4;
}

.airline-tag.all-tag {
  background: #28a745;
  color: white;
  border-color: #28a745;
  font-weight: 600;
}

.airline-tag.all-tag:hover {
  background: #218838;
  border-color: #1e7e34;
}

.no-airlines {
  color: #999;
  font-size: 12px;
  font-style: italic;
  margin: 0;
}

/* 기존 항공사 차트 (호환성 유지) */
#airline-chart h4 {
  margin: 0 0 15px 0;
  font-size: 14px;
  font-weight: 600;
  color: #333;
}

.airline-bar {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
  gap: 10px;
}

.airline-name {
  min-width: 80px;
  font-size: 12px;
  color: #666;
}

.airline-bar-fill {
  background: linear-gradient(90deg, #4285f4, #34a853);
  height: 20px;
  border-radius: 10px;
  min-width: 4px;
  transition: width 0.3s ease;
}

.airline-count {
  font-size: 12px;
  font-weight: 600;
  color: #333;
  min-width: 20px;
  text-align: right;
}

/* 반응형 디자인 */
@media (max-width: 1200px) {
  #naver-flight-viz-panel {
    width: 95vw;
    max-width: 95vw;
  }
}

@media (max-width: 768px) {
  #naver-flight-viz-panel {
    width: 95vw;
    max-width: 95vw;
    top: 70px;
    max-height: 80vh;
  }

  .viz-content {
    flex-direction: column;
    height: auto;
    max-height: calc(80vh - 60px);
  }

  .viz-left-panel {
    width: 100%;
    border-right: none;
    border-bottom: 1px solid #e0e0e0;
    max-height: 300px;
  }

  .viz-right-panel {
    width: 100%;
    max-height: 400px;
  }

  #naver-flight-viz-toggle {
    right: 15px;
    top: 15px;
    width: 45px;
    height: 45px;
    font-size: 18px;
  }
}

/* 스크롤바 스타일링 */
.viz-content::-webkit-scrollbar {
  width: 6px;
}

.viz-content::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.viz-content::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.viz-content::-webkit-scrollbar-thumb:hover {
  background: #a1a1a1;
}

/* 카드 차트 헤더 스타일 */
.card-chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.card-chart-header h4 {
  margin: 0;
  font-size: 14px;
  font-weight: 600;
  color: #333;
}

/* 토글 스위치 스타일 */
.card-filter-toggle {
  display: flex;
  align-items: center;
  gap: 8px;
}

.toggle-switch {
  position: relative;
  display: inline-flex;
  align-items: center;
  cursor: pointer;
  font-size: 12px;
  color: #666;
  gap: 8px;
}

.toggle-switch input[type="checkbox"] {
  opacity: 0;
  width: 0;
  height: 0;
}

.toggle-slider {
  position: relative;
  width: 40px;
  height: 20px;
  background-color: #ccc;
  border-radius: 20px;
  transition: background-color 0.3s;
}

.toggle-slider:before {
  content: "";
  position: absolute;
  height: 16px;
  width: 16px;
  left: 2px;
  top: 2px;
  background-color: white;
  border-radius: 50%;
  transition: transform 0.3s;
}

.toggle-switch input:checked + .toggle-slider {
  background-color: #4285f4;
}

.toggle-switch input:checked + .toggle-slider:before {
  transform: translateX(20px);
}

.toggle-label {
  font-size: 11px;
  color: #666;
  user-select: none;
}